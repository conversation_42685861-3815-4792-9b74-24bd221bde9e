"use client"

import { useState, useEffect, useRef } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Split, Merge, ZoomIn, ZoomOut, RotateCcw, ArrowLeft, ArrowRight, Download, Maximize, List } from "lucide-react"
import { cn } from "@/lib/utils"
import type { DocumentVersion } from "./pdf-version-control"

interface PDFVersionDiffViewerProps {
  version1: DocumentVersion
  version2: DocumentVersion
  pdfDocument1: any
  pdfDocument2: any
  onClose: () => void
}

interface DiffHighlight {
  id: string
  type: "added" | "removed" | "modified"
  page: number
  bounds: { x: number; y: number; width: number; height: number }
  content?: string
  category: "text" | "annotation" | "form" | "image"
}

export default function PDFVersionDiffViewer({
  version1,
  version2,
  pdfDocument1,
  pdfDocument2,
  onClose,
}: PDFVersionDiffViewerProps) {
  const [viewMode, setViewMode] = useState<"side-by-side" | "overlay" | "unified">("side-by-side")
  const [currentPage, setCurrentPage] = useState(1)
  const [scale, setScale] = useState(1.0)
  const [showChangesOnly, setShowChangesOnly] = useState(false)
  const [filterCategory, setFilterCategory] = useState<"all" | "text" | "annotation" | "form" | "image">("all")
  const [diffHighlights, setDiffHighlights] = useState<DiffHighlight[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(true)
  const canvasRef1 = useRef<HTMLCanvasElement>(null)
  const canvasRef2 = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    analyzeDifferences()
  }, [version1, version2, pdfDocument1, pdfDocument2])

  const analyzeDifferences = async () => {
    setIsAnalyzing(true)
    try {
      // Simulate diff analysis
      const mockDiffs: DiffHighlight[] = [
        {
          id: "diff1",
          type: "added",
          page: 1,
          bounds: { x: 100, y: 200, width: 200, height: 50 },
          content: "New annotation added",
          category: "annotation",
        },
        {
          id: "diff2",
          type: "modified",
          page: 2,
          bounds: { x: 150, y: 300, width: 180, height: 30 },
          content: "Form field validation changed",
          category: "form",
        },
        {
          id: "diff3",
          type: "removed",
          page: 3,
          bounds: { x: 200, y: 150, width: 100, height: 40 },
          content: "Text removed",
          category: "text",
        },
      ]

      setDiffHighlights(mockDiffs)
    } catch (error) {
      console.error("Failed to analyze differences:", error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const renderPageWithDiffs = (pageNum: number, version: "v1" | "v2") => {
    const pageDiffs = diffHighlights.filter(
      (diff) => diff.page === pageNum && (filterCategory === "all" || diff.category === filterCategory),
    )

    return (
      <div className="relative">
        <canvas
          ref={version === "v1" ? canvasRef1 : canvasRef2}
          className="border shadow-sm"
          style={{ transform: `scale(${scale})`, transformOrigin: "top left" }}
        />

        {/* Diff overlays */}
        {pageDiffs.map((diff) => (
          <div
            key={diff.id}
            className={cn(
              "absolute border-2 pointer-events-none",
              diff.type === "added" && "border-green-500 bg-green-100/30",
              diff.type === "removed" && "border-red-500 bg-red-100/30",
              diff.type === "modified" && "border-yellow-500 bg-yellow-100/30",
            )}
            style={{
              left: diff.bounds.x * scale,
              top: diff.bounds.y * scale,
              width: diff.bounds.width * scale,
              height: diff.bounds.height * scale,
            }}
          >
            <div
              className={cn(
                "absolute -top-6 left-0 px-2 py-1 text-xs font-medium rounded",
                diff.type === "added" && "bg-green-500 text-white",
                diff.type === "removed" && "bg-red-500 text-white",
                diff.type === "modified" && "bg-yellow-500 text-white",
              )}
            >
              {diff.type}
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderSideBySideView = () => (
    <div className="grid grid-cols-2 gap-4 h-full">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm">Version {version1.version}</h4>
          <Badge variant="outline">{version1.timestamp.toLocaleDateString()}</Badge>
        </div>
        <ScrollArea className="h-full">{renderPageWithDiffs(currentPage, "v1")}</ScrollArea>
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm">Version {version2.version}</h4>
          <Badge variant="outline">{version2.timestamp.toLocaleDateString()}</Badge>
        </div>
        <ScrollArea className="h-full">{renderPageWithDiffs(currentPage, "v2")}</ScrollArea>
      </div>
    </div>
  )

  const renderOverlayView = () => (
    <div className="relative h-full">
      <ScrollArea className="h-full">
        <div className="relative">
          {renderPageWithDiffs(currentPage, "v1")}
          <div className="absolute inset-0 opacity-50">{renderPageWithDiffs(currentPage, "v2")}</div>
        </div>
      </ScrollArea>
    </div>
  )

  const renderUnifiedView = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {diffHighlights
          .filter((diff) => diff.page === currentPage)
          .filter((diff) => filterCategory === "all" || diff.category === filterCategory)
          .map((diff) => (
            <Card key={diff.id} className="p-4">
              <div className="flex items-start gap-3">
                <Badge
                  variant={diff.type === "added" ? "default" : diff.type === "removed" ? "destructive" : "secondary"}
                >
                  {diff.type}
                </Badge>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm capitalize">{diff.category}</span>
                    <span className="text-xs text-muted-foreground">Page {diff.page}</span>
                  </div>
                  {diff.content && <p className="text-sm text-muted-foreground">{diff.content}</p>}
                  <div className="text-xs text-muted-foreground mt-1">
                    Position: ({diff.bounds.x}, {diff.bounds.y}) - {diff.bounds.width}×{diff.bounds.height}
                  </div>
                </div>
              </div>
            </Card>
          ))}
      </div>
    </div>
  )

  const filteredDiffs = diffHighlights.filter((diff) => filterCategory === "all" || diff.category === filterCategory)

  const changesByType = {
    added: filteredDiffs.filter((d) => d.type === "added").length,
    removed: filteredDiffs.filter((d) => d.type === "removed").length,
    modified: filteredDiffs.filter((d) => d.type === "modified").length,
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onClose}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="font-semibold">Version Comparison</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Diff
            </Button>
            <Button variant="outline" size="sm">
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="side-by-side">
                  <div className="flex items-center gap-2">
                    <Split className="h-4 w-4" />
                    Side by Side
                  </div>
                </SelectItem>
                <SelectItem value="overlay">
                  <div className="flex items-center gap-2">
                    <Merge className="h-4 w-4" />
                    Overlay
                  </div>
                </SelectItem>
                <SelectItem value="unified">
                  <div className="flex items-center gap-2">
                    <List className="h-4 w-4" />
                    Unified
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterCategory} onValueChange={(value: any) => setFilterCategory(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Changes</SelectItem>
                <SelectItem value="text">Text</SelectItem>
                <SelectItem value="annotation">Annotations</SelectItem>
                <SelectItem value="form">Forms</SelectItem>
                <SelectItem value="image">Images</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" onClick={() => setScale((s) => Math.max(0.5, s - 0.25))}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium min-w-[4rem] text-center">{Math.round(scale * 100)}%</span>
              <Button variant="ghost" size="sm" onClick={() => setScale((s) => Math.min(3.0, s + 0.25))}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setScale(1.0)}>
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm">
              <Badge variant="default" className="bg-green-500">
                +{changesByType.added}
              </Badge>
              <Badge variant="destructive">-{changesByType.removed}</Badge>
              <Badge variant="secondary">~{changesByType.modified}</Badge>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage <= 1}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Page {currentPage} of {Math.max(version1.metadata.pageCount, version2.metadata.pageCount)}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  setCurrentPage((p) =>
                    Math.min(Math.max(version1.metadata.pageCount, version2.metadata.pageCount), p + 1),
                  )
                }
                disabled={currentPage >= Math.max(version1.metadata.pageCount, version2.metadata.pageCount)}
              >
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isAnalyzing ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-sm text-muted-foreground">Analyzing differences...</p>
            </div>
          </div>
        ) : (
          <div className="h-full p-4">
            {viewMode === "side-by-side" && renderSideBySideView()}
            {viewMode === "overlay" && renderOverlayView()}
            {viewMode === "unified" && renderUnifiedView()}
          </div>
        )}
      </div>
    </div>
  )
}
