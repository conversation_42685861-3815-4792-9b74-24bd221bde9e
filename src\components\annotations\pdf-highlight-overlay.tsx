"use client";

import { useEffect, useState, useCallback } from "react";

interface HighlightRect {
  left: number;
  top: number;
  width: number;
  height: number;
  text: string;
}

interface PDFHighlightOverlayProps {
  pdfDocument: unknown;
  pageNumber: number;
  scale: number;
  rotation: number;
  searchText: string;
  searchOptions: { caseSensitive: boolean; wholeWords: boolean };
  isCurrentSearchPage: boolean;
}

export default function PDFHighlightOverlay({
  pdfDocument,
  pageNumber,
  scale,
  rotation,
  searchText,
  searchOptions,
  isCurrentSearchPage,
}: PDFHighlightOverlayProps) {
  const [highlights, setHighlights] = useState<HighlightRect[]>([]);
  const [pageSize, setPageSize] = useState({ width: 0, height: 0 });

  const calculateHighlights = useCallback(async () => {
    if (!pdfDocument || !searchText.trim()) {
      setHighlights([]);
      return;
    }

    try {
      // Access the PDF document properly
      let doc = pdfDocument;
      if (pdfDocument._pdfInfo && pdfDocument._pdfInfo.pdfDocument) {
        doc = pdfDocument._pdfInfo.pdfDocument;
      }

      if (!doc || typeof doc.getPage !== "function") {
        return;
      }

      const page = await doc.getPage(pageNumber);
      const viewport = page.getViewport({ scale: 1.0, rotation: 0 });
      const textContent = await page.getTextContent();

      setPageSize({ width: viewport.width, height: viewport.height });

      // Create search regex
      let searchRegex: RegExp;
      if (searchOptions.wholeWords) {
        const pattern = `\\b${searchText.replace(
          /[.*+?^${}()|[\]\\]/g,
          "\\$&"
        )}\\b`;
        searchRegex = new RegExp(
          pattern,
          searchOptions.caseSensitive ? "g" : "gi"
        );
      } else {
        searchRegex = new RegExp(
          searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          searchOptions.caseSensitive ? "g" : "gi"
        );
      }

      const newHighlights: HighlightRect[] = [];

      textContent.items.forEach((item: { str?: string; transform?: number[]; width?: number; height?: number }) => {
        if ("str" in item && searchRegex.test(item.str)) {
          // Get text item properties
          const transform = item.transform;
          const width = item.width;
          const height = item.height;

          // Calculate position from transform matrix
          const x = transform[4];
          const y = transform[5];

          // Convert PDF coordinates to screen coordinates
          // PDF coordinates have origin at bottom-left, we need top-left
          const rect = {
            left: x,
            top: viewport.height - y - height,
            width: width,
            height: height,
            text: item.str,
          };

          newHighlights.push(rect);
        }
      });

      setHighlights(newHighlights);
    } catch (error) {
      console.warn("Error calculating highlights:", error);
      setHighlights([]);
    }
  }, [pdfDocument, pageNumber, searchText, searchOptions]);

  useEffect(() => {
    calculateHighlights();
  }, [calculateHighlights]);

  if (highlights.length === 0) {
    return null;
  }

  return (
    <div
      className="absolute inset-0 pointer-events-none"
      style={{
        transform: `scale(${scale}) rotate(${rotation}deg)`,
        transformOrigin: "top left",
      }}
    >
      {highlights.map((highlight, index) => (
        <div
          key={index}
          className={`absolute border-2 ${
            isCurrentSearchPage
              ? "bg-yellow-300/40 border-yellow-500/60"
              : "bg-blue-300/30 border-blue-500/50"
          } rounded-sm transition-all duration-200`}
          style={{
            left: `${(highlight.left / pageSize.width) * 100}%`,
            top: `${(highlight.top / pageSize.height) * 100}%`,
            width: `${(highlight.width / pageSize.width) * 100}%`,
            height: `${(highlight.height / pageSize.height) * 100}%`,
          }}
          title={`Match: "${highlight.text}"`}
        />
      ))}
    </div>
  );
}
