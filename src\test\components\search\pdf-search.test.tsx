import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import PDFSearch from '@/components/search/pdf-search'

describe('PDFSearch - Consolidated Component', () => {
  const defaultProps = {
    searchText: '',
    onSearchChange: vi.fn(),
    onClose: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Search Functionality', () => {
    it('renders search input with current text', () => {
      render(<PDFSearch {...defaultProps} searchText="test query" />)
      
      const searchInput = screen.getByDisplayValue('test query')
      expect(searchInput).toBeInTheDocument()
    })

    it('calls onSearchChange when input changes', async () => {
      const user = userEvent.setup()
      const onSearchChange = vi.fn()
      
      render(<PDFSearch {...defaultProps} onSearchChange={onSearchChange} />)
      
      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, 'new search')
      
      expect(onSearchChange).toHaveBeenCalledWith('new search')
    })

    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup()
      const onClose = vi.fn()
      
      render(<PDFSearch {...defaultProps} onClose={onClose} />)
      
      const closeButton = screen.getByRole('button', { name: /close/i })
      await user.click(closeButton)
      
      expect(onClose).toHaveBeenCalled()
    })
  })

  describe('Enhanced Search Features', () => {
    it('displays search options when available', () => {
      render(
        <PDFSearch 
          {...defaultProps} 
          variant="enhanced"
          searchOptions={{ caseSensitive: false, wholeWords: true }}
        />
      )
      
      // Should show search options UI
      expect(screen.getByRole('checkbox', { name: /case sensitive/i })).toBeInTheDocument()
      expect(screen.getByRole('checkbox', { name: /whole words/i })).toBeInTheDocument()
    })

    it('handles search options changes', async () => {
      const user = userEvent.setup()
      const onSearchOptionsChange = vi.fn()
      
      render(
        <PDFSearch 
          {...defaultProps} 
          variant="enhanced"
          searchOptions={{ caseSensitive: false, wholeWords: false }}
          onSearchOptionsChange={onSearchOptionsChange}
        />
      )
      
      const caseSensitiveCheckbox = screen.getByRole('checkbox', { name: /case sensitive/i })
      await user.click(caseSensitiveCheckbox)
      
      expect(onSearchOptionsChange).toHaveBeenCalledWith({ caseSensitive: true, wholeWords: false })
    })

    it('displays search results when provided', () => {
      const searchResults = [
        {
          pageIndex: 0,
          textItems: [
            { str: 'test result', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }
          ]
        },
        {
          pageIndex: 1,
          textItems: [
            { str: 'another result', matches: [], transform: [], width: 100, height: 20, itemIndex: 1 }
          ]
        }
      ]
      
      render(
        <PDFSearch 
          {...defaultProps} 
          searchResults={searchResults}
          variant="unified"
        />
      )
      
      expect(screen.getByText(/2 results/i)).toBeInTheDocument()
    })

    it('handles page navigation from search results', async () => {
      const user = userEvent.setup()
      const onPageSelect = vi.fn()
      const searchResults = [
        {
          pageIndex: 0,
          textItems: [
            { str: 'test result', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }
          ]
        }
      ]
      
      render(
        <PDFSearch 
          {...defaultProps} 
          searchResults={searchResults}
          onPageSelect={onPageSelect}
          variant="unified"
        />
      )
      
      const resultItem = screen.getByText(/page 1/i)
      await user.click(resultItem)
      
      expect(onPageSelect).toHaveBeenCalledWith(1)
    })
  })

  describe('Search Variants', () => {
    it('renders simple variant correctly', () => {
      render(<PDFSearch {...defaultProps} variant="simple" />)
      
      // Simple variant should have basic search input
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.queryByText(/search options/i)).not.toBeInTheDocument()
    })

    it('renders enhanced variant with additional features', () => {
      render(<PDFSearch {...defaultProps} variant="enhanced" />)
      
      // Enhanced variant should have search options
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.getByText(/search options/i)).toBeInTheDocument()
    })

    it('renders unified variant with full functionality', () => {
      render(
        <PDFSearch 
          {...defaultProps} 
          variant="unified"
          searchResults={[]}
        />
      )
      
      // Unified variant should have all features
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.getByText(/search options/i)).toBeInTheDocument()
      expect(screen.getByText(/results/i)).toBeInTheDocument()
    })
  })

  describe('Legacy API Compatibility', () => {
    it('supports legacy onSearch callback', async () => {
      const user = userEvent.setup()
      const onSearch = vi.fn()
      
      render(<PDFSearch {...defaultProps} onSearch={onSearch} />)
      
      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, 'legacy search{enter}')
      
      expect(onSearch).toHaveBeenCalledWith('legacy search')
    })

    it('supports legacy navigation callbacks', async () => {
      const user = userEvent.setup()
      const onNavigateResults = vi.fn()
      
      render(
        <PDFSearch 
          {...defaultProps} 
          onNavigateResults={onNavigateResults}
          searchResults={[
            { pageIndex: 0, textItems: [{ str: 'result 1', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }] },
            { pageIndex: 1, textItems: [{ str: 'result 2', matches: [], transform: [], width: 100, height: 20, itemIndex: 1 }] }
          ]}
          currentSearchIndex={0}
        />
      )
      
      const nextButton = screen.getByRole('button', { name: /next/i })
      await user.click(nextButton)
      
      expect(onNavigateResults).toHaveBeenCalledWith('next')
    })

    it('supports legacy clear search callback', async () => {
      const user = userEvent.setup()
      const onClearSearch = vi.fn()
      
      render(
        <PDFSearch 
          {...defaultProps} 
          searchText="some text"
          onClearSearch={onClearSearch}
        />
      )
      
      const clearButton = screen.getByRole('button', { name: /clear/i })
      await user.click(clearButton)
      
      expect(onClearSearch).toHaveBeenCalled()
    })
  })

  describe('Search State Management', () => {
    it('shows loading state when searching', () => {
      render(<PDFSearch {...defaultProps} isSearching={true} />)
      
      expect(screen.getByTestId('search-loading')).toBeInTheDocument()
    })

    it('highlights current search result', () => {
      const searchResults = [
        { pageIndex: 0, textItems: [{ str: 'result 1', matches: [], transform: [], width: 100, height: 20, itemIndex: 0 }] },
        { pageIndex: 1, textItems: [{ str: 'result 2', matches: [], transform: [], width: 100, height: 20, itemIndex: 1 }] }
      ]
      
      render(
        <PDFSearch 
          {...defaultProps} 
          searchResults={searchResults}
          currentSearchIndex={1}
        />
      )
      
      const currentResult = screen.getByTestId('search-result-1')
      expect(currentResult).toHaveClass('bg-primary')
    })

    it('handles empty search results', () => {
      render(
        <PDFSearch 
          {...defaultProps} 
          searchResults={[]}
          searchText="no results"
        />
      )
      
      expect(screen.getByText(/no results found/i)).toBeInTheDocument()
    })
  })

  describe('Keyboard Navigation', () => {
    it('handles Enter key for search', async () => {
      const user = userEvent.setup()
      const onSearch = vi.fn()
      
      render(<PDFSearch {...defaultProps} onSearch={onSearch} />)
      
      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, 'keyboard search{enter}')
      
      expect(onSearch).toHaveBeenCalledWith('keyboard search')
    })

    it('handles Escape key to close', async () => {
      const user = userEvent.setup()
      const onClose = vi.fn()
      
      render(<PDFSearch {...defaultProps} onClose={onClose} />)
      
      const searchInput = screen.getByRole('textbox')
      await user.type(searchInput, '{escape}')
      
      expect(onClose).toHaveBeenCalled()
    })
  })
})