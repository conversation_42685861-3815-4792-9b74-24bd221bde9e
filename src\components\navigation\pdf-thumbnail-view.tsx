"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Grid3X3, List, ZoomIn, ZoomOut, Eye, Bookmark } from "lucide-react"
import { cn } from "@/lib/utils"
import type { Annotation } from "../annotations/pdf-annotations"

interface PDFThumbnailViewProps {
  pdfDocument: any
  numPages: number
  currentPage: number
  onPageSelect: (page: number) => void
  annotations: Annotation[]
  bookmarks: Array<{ id: string; page: number; title: string; timestamp: number }>
}

interface ThumbnailData {
  pageNumber: number
  imageUrl: string | null
  isLoading: boolean
  error: boolean
}

export default function PDFThumbnailView({
  pdfDocument,
  numPages,
  currentPage,
  onPageSelect,
  annotations,
  bookmarks,
}: PDFThumbnailViewProps) {
  const [thumbnails, setThumbnails] = useState<ThumbnailData[]>([])
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [thumbnailScale, setThumbnailScale] = useState(0.3)
  const [isGenerating, setIsGenerating] = useState(false)

  // Initialize thumbnail data
  useEffect(() => {
    const initialThumbnails: ThumbnailData[] = Array.from({ length: numPages }, (_, i) => ({
      pageNumber: i + 1,
      imageUrl: null,
      isLoading: false,
      error: false,
    }))
    setThumbnails(initialThumbnails)
  }, [numPages])

  const generateThumbnail = useCallback(
    async (pageNumber: number) => {
      if (!pdfDocument) return

      setThumbnails((prev) =>
        prev.map((thumb) => (thumb.pageNumber === pageNumber ? { ...thumb, isLoading: true, error: false } : thumb)),
      )

      try {
        let doc = pdfDocument
        if (pdfDocument._pdfInfo && pdfDocument._pdfInfo.pdfDocument) {
          doc = pdfDocument._pdfInfo.pdfDocument
        }

        if (!doc || typeof doc.getPage !== "function") {
          throw new Error("Invalid PDF document")
        }

        const page = await doc.getPage(pageNumber)
        const viewport = page.getViewport({ scale: thumbnailScale })

        const canvas = document.createElement("canvas")
        const context = canvas.getContext("2d")

        if (!context) {
          throw new Error("Could not get canvas context")
        }

        canvas.width = viewport.width
        canvas.height = viewport.height

        await page.render({
          canvasContext: context,
          viewport: viewport,
        }).promise

        const imageUrl = canvas.toDataURL("image/jpeg", 0.8)

        setThumbnails((prev) =>
          prev.map((thumb) =>
            thumb.pageNumber === pageNumber ? { ...thumb, imageUrl, isLoading: false, error: false } : thumb,
          ),
        )
      } catch (error) {
        console.error(`Failed to generate thumbnail for page ${pageNumber}:`, error)
        setThumbnails((prev) =>
          prev.map((thumb) => (thumb.pageNumber === pageNumber ? { ...thumb, isLoading: false, error: true } : thumb)),
        )
      }
    },
    [pdfDocument, thumbnailScale],
  )

  const generateAllThumbnails = useCallback(async () => {
    if (!pdfDocument || isGenerating) return

    setIsGenerating(true)

    // Generate thumbnails in batches to avoid overwhelming the browser
    const batchSize = 5
    for (let i = 0; i < numPages; i += batchSize) {
      const batch = Array.from({ length: Math.min(batchSize, numPages - i) }, (_, j) => i + j + 1)

      await Promise.all(batch.map((pageNumber) => generateThumbnail(pageNumber)))

      // Small delay between batches
      if (i + batchSize < numPages) {
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
    }

    setIsGenerating(false)
  }, [pdfDocument, numPages, generateThumbnail, isGenerating])

  const getPageAnnotations = (pageNumber: number) => {
    return annotations.filter((ann) => ann.pageNumber === pageNumber)
  }

  const getPageBookmarks = (pageNumber: number) => {
    return bookmarks.filter((bookmark) => bookmark.page === pageNumber)
  }

  const increaseThumbnailSize = () => {
    setThumbnailScale((prev) => Math.min(0.6, prev + 0.1))
  }

  const decreaseThumbnailSize = () => {
    setThumbnailScale((prev) => Math.max(0.2, prev - 0.1))
  }

  // Auto-generate thumbnails for visible pages
  useEffect(() => {
    if (pdfDocument && thumbnails.length > 0) {
      // Generate thumbnail for current page and nearby pages
      const pagesToGenerate = [currentPage, Math.max(1, currentPage - 1), Math.min(numPages, currentPage + 1)]

      pagesToGenerate.forEach((pageNumber) => {
        const thumbnail = thumbnails.find((t) => t.pageNumber === pageNumber)
        if (thumbnail && !thumbnail.imageUrl && !thumbnail.isLoading && !thumbnail.error) {
          generateThumbnail(pageNumber)
        }
      })
    }
  }, [currentPage, pdfDocument, thumbnails, generateThumbnail, numPages])

  const renderThumbnail = (thumbnail: ThumbnailData) => {
    const pageAnnotations = getPageAnnotations(thumbnail.pageNumber)
    const pageBookmarks = getPageBookmarks(thumbnail.pageNumber)
    const isCurrentPage = thumbnail.pageNumber === currentPage

    const thumbnailContent = (
      <div
        className={cn(
          "relative border rounded-lg overflow-hidden cursor-pointer transition-all hover:shadow-md",
          isCurrentPage ? "ring-2 ring-primary shadow-lg" : "border-muted-foreground/20",
        )}
        onClick={() => onPageSelect(thumbnail.pageNumber)}
      >
        {/* Thumbnail Image */}
        <div
          className="bg-muted/30 flex items-center justify-center"
          style={{
            width: `${(200 * thumbnailScale) / 0.3}px`,
            height: `${(280 * thumbnailScale) / 0.3}px`,
          }}
        >
          {thumbnail.isLoading ? (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          ) : thumbnail.error ? (
            <div className="text-center p-4">
              <div className="text-muted-foreground text-sm">Failed to load</div>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation()
                  generateThumbnail(thumbnail.pageNumber)
                }}
                className="mt-2"
              >
                Retry
              </Button>
            </div>
          ) : thumbnail.imageUrl ? (
            <img
              src={thumbnail.imageUrl || "/placeholder.svg"}
              alt={`Page ${thumbnail.pageNumber}`}
              className="w-full h-full object-contain"
            />
          ) : (
            <div className="text-center p-4">
              <Eye className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
              <div className="text-muted-foreground text-sm">Click to generate</div>
            </div>
          )}
        </div>

        {/* Page Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/80 text-white p-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Page {thumbnail.pageNumber}</span>
            <div className="flex items-center gap-1">
              {pageBookmarks.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  <Bookmark className="h-3 w-3 mr-1" />
                  {pageBookmarks.length}
                </Badge>
              )}
              {pageAnnotations.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {pageAnnotations.length}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Current Page Indicator */}
        {isCurrentPage && (
          <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
            ●
          </div>
        )}
      </div>
    )

    if (viewMode === "list") {
      return (
        <div key={thumbnail.pageNumber} className="flex items-center gap-4 p-3 border rounded-lg">
          <div className="flex-shrink-0">{thumbnailContent}</div>
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Page {thumbnail.pageNumber}</h4>
              <div className="flex items-center gap-2">
                {pageBookmarks.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Bookmark className="h-3 w-3 mr-1" />
                    {pageBookmarks.length} bookmark{pageBookmarks.length !== 1 ? "s" : ""}
                  </Badge>
                )}
                {pageAnnotations.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {pageAnnotations.length} annotation{pageAnnotations.length !== 1 ? "s" : ""}
                  </Badge>
                )}
              </div>
            </div>

            {pageBookmarks.length > 0 && (
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Bookmarks:</div>
                {pageBookmarks.slice(0, 2).map((bookmark) => (
                  <div key={bookmark.id} className="text-sm text-muted-foreground truncate">
                    • {bookmark.title}
                  </div>
                ))}
                {pageBookmarks.length > 2 && (
                  <div className="text-xs text-muted-foreground">+{pageBookmarks.length - 2} more</div>
                )}
              </div>
            )}

            {pageAnnotations.length > 0 && (
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Annotations:</div>
                {pageAnnotations.slice(0, 2).map((annotation) => (
                  <div key={annotation.id} className="text-sm text-muted-foreground truncate">
                    • {annotation.type}: {annotation.content || "No content"}
                  </div>
                ))}
                {pageAnnotations.length > 2 && (
                  <div className="text-xs text-muted-foreground">+{pageAnnotations.length - 2} more</div>
                )}
              </div>
            )}
          </div>
        </div>
      )
    }

    return <div key={thumbnail.pageNumber}>{thumbnailContent}</div>
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Grid3X3 className="h-5 w-5" />
              Thumbnails
            </CardTitle>
            <CardDescription>Navigate through pages visually</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant={viewMode === "grid" ? "default" : "outline"} size="sm" onClick={() => setViewMode("grid")}>
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button variant={viewMode === "list" ? "default" : "outline"} size="sm" onClick={() => setViewMode("list")}>
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={decreaseThumbnailSize}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <span className="text-sm text-muted-foreground">{Math.round(thumbnailScale * 100)}%</span>
            <Button size="sm" variant="outline" onClick={increaseThumbnailSize}>
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>

          <Button size="sm" onClick={generateAllThumbnails} disabled={isGenerating}>
            {isGenerating ? "Generating..." : "Generate All"}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div
            className={cn(
              viewMode === "grid" ? "grid gap-4" : "space-y-4",
              viewMode === "grid" && "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
            )}
          >
            {thumbnails.map(renderThumbnail)}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
