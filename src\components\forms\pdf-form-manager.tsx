"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  FileText,
  Save,
  Download,
  Upload,
  RefreshCw,
  AlertCircle,
  Eye,
  EyeOff,
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Plus,
  Lock,
  Square,
  Circle,
  Type,
  CheckSquare,
  List,
  Calendar,
  Hash,
  AtSign,
  Phone,
  Link,

} from "lucide-react";
import { toast } from "sonner";

export type FormFieldType =
  | "text"
  | "textarea"
  | "multiline"
  | "checkbox"
  | "radio"
  | "select"
  | "dropdown"
  | "listbox"
  | "signature"
  | "date"
  | "number"
  | "email"
  | "phone"
  | "url"
  | "file"
  | "image"
  | "barcode"
  | "button"
  | "submit"
  | "reset";

export interface FormField {
  id: string;
  name: string;
  type: FormFieldType;
  value: unknown;
  defaultValue?: unknown;
  required: boolean;
  readonly: boolean;
  disabled?: boolean;
  options?: FormFieldOption[]; // Enhanced for select/radio fields
  validation?: FormFieldValidation;
  validationRules?: ValidationRule[];
  customValidator?: (value: unknown) => ValidationResult;
  position: {
    pageNumber: number;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  appearance: {
    fontSize: number;
    fontColor: string;
    backgroundColor: string;
    borderColor: string;
    borderWidth: number;
    fontFamily?: string;
  };
  metadata: {
    tooltip?: string;
    placeholder?: string;
    helpText?: string;
    group?: string;
    category?: string;
    tags?: string[];
    created: number;
    modified: number;
  };
  // Enhanced properties
  label?: string;
  maxLength?: number;
  minLength?: number;
  min?: number;
  max?: number;
  step?: number;
  pattern?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  tabIndex?: number;
  multiSelect?: boolean; // For listbox fields
  dependencies?: FormFieldDependency[];
  // State
  isSelected?: boolean;
  isFocused?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  isModified?: boolean;
  calculatedValue?: unknown;
}

export interface FormFieldOption {
  value: unknown;
  label: string;
  disabled?: boolean;
  selected?: boolean;
}

export interface FormFieldValidation {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
  // Legacy validation properties for backward compatibility
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
}

export interface ValidationRule {
  type:
    | "required"
    | "minLength"
    | "maxLength"
    | "pattern"
    | "email"
    | "url"
    | "number"
    | "date"
    | "custom";
  value?: unknown;
  message: string;
  severity: "error" | "warning";
}

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  severity?: "error" | "warning";
}

export interface FormFieldDependency {
  fieldId: string;
  condition:
    | "equals"
    | "not_equals"
    | "contains"
    | "greater_than"
    | "less_than"
    | "empty"
    | "not_empty";
  value?: unknown;
  action: "show" | "hide" | "enable" | "disable" | "set_value" | "clear_value";
  targetValue?: unknown;
}

export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  fields: Omit<FormField, "id" | "pageNumber" | "x" | "y">[];
  previewImage?: string;
  usageCount: number;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  tags: string[];
}

export interface FormValidationResult {
  isValid: boolean;
  fields: Record<string, FormFieldValidation>;
  summary: {
    totalFields: number;
    validFields: number;
    requiredFields: number;
    filledRequiredFields: number;
    errors: number;
    warnings: number;
  };
}

export interface FormData {
  [fieldName: string]: unknown;
}

interface PDFFormManagerProps {
  pdfDocument: unknown;
  numPages?: number;
  currentPage?: number;
  onFormFieldsChange: (fields: FormField[]) => void;
  onFormDataChange: (data: FormData) => void;
  // Enhanced props
  formFields?: FormField[];
  formData?: FormData;
  validationRules?: ValidationRule[];
  formTemplates?: FormTemplate[];

  // Form modes
  mode?: "view" | "edit" | "design";
  enableValidation?: boolean;
  enableTemplates?: boolean;

  // Event handlers
  onFieldAdd?: (field: FormField) => void;
  onFieldUpdate?: (id: string, field: Partial<FormField>) => void;
  onFieldDelete?: (id: string) => void;
  onFormSave?: (formData: FormData) => void;
  onFormLoad?: (formData: FormData) => void;

  // Legacy enhanced props
  templates?: FormTemplate[];
  onTemplateApply?: (template: FormTemplate) => void;
  onTemplateSave?: (name: string, description: string) => void;
  onFormExport?: (format: "json" | "fdf" | "xfdf" | "csv") => void;
  onFormImport?: (data: unknown, format: string) => void;
  isDesignMode?: boolean;
  onDesignModeToggle?: () => void;
  onFormValidate?: (showErrors?: boolean) => FormValidationResult;
  onFormSubmit?: (data: FormData, validation: FormValidationResult) => void;
}

export default function PDFFormManager({
  pdfDocument,
  numPages = 1,
  currentPage = 1,
  onFormFieldsChange,
  onFormDataChange,
  // Enhanced props
  formFields: initialFormFields,
  formData: initialFormData,
  mode = "edit",
  // Event handlers
  // Legacy enhanced props
  templates = [],
  onTemplateApply,
  onTemplateSave,
  onFormExport,
  isDesignMode = false,
  onDesignModeToggle,
}: PDFFormManagerProps) {
  const [formFields, setFormFields] = useState<FormField[]>(
    initialFormFields || []
  );
  const [formData, setFormData] = useState<FormData>(initialFormData || {});
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [validationResult, setValidationResult] =
    useState<FormValidationResult | null>(null);
  const [showValidation, setShowValidation] = useState(true);
  const [formMode, setFormMode] = useState<"view" | "edit" | "design">(
    mode === "design" || isDesignMode
      ? "design"
      : mode === "view"
      ? "view"
      : "edit"
  );
  const [activePanel, setActivePanel] = useState<
    "fields" | "validation" | "templates"
  >("fields");

  const [selectedFieldType, setSelectedFieldType] =
    useState<FormFieldType>("text");

  // Extract form fields from PDF
  const extractFormFields = useCallback(async () => {
    if (!pdfDocument) return;

    setIsLoading(true);
    try {
      let doc = pdfDocument;
      if (pdfDocument._pdfInfo && pdfDocument._pdfInfo.pdfDocument) {
        doc = pdfDocument._pdfInfo.pdfDocument;
      }

      const fields: FormField[] = [];

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        try {
          const page = await doc.getPage(pageNum);
          const annotations = await page.getAnnotations();

          annotations.forEach((annotation: { subtype?: string; fieldName?: string; fieldType?: string; multiLine?: boolean; checkBox?: boolean; radioButton?: boolean; combo?: boolean; list?: boolean; signature?: boolean }, index: number) => {
            if (annotation.subtype === "Widget" && annotation.fieldName) {
              const field: FormField = {
                id: `${pageNum}-${index}-${annotation.fieldName}`,
                name: annotation.fieldName,
                type: getFieldType(annotation),
                value: annotation.fieldValue || "",
                defaultValue: annotation.defaultValue || "",
                required: annotation.required || false,
                readonly: annotation.readOnly || false,
                options: annotation.options || [],
                validation: extractValidation(annotation),
                position: {
                  pageNumber: pageNum,
                  x: annotation.rect[0],
                  y: annotation.rect[1],
                  width: annotation.rect[2] - annotation.rect[0],
                  height: annotation.rect[3] - annotation.rect[1],
                },
                appearance: {
                  fontSize: annotation.fontSize || 12,
                  fontColor: annotation.color || "#000000",
                  backgroundColor: annotation.backgroundColor || "#ffffff",
                  borderColor: annotation.borderColor || "#cccccc",
                  borderWidth: annotation.borderWidth || 1,
                },
                metadata: {
                  tooltip: annotation.alternativeText,
                  placeholder: annotation.placeholder,
                  created: Date.now(),
                  modified: Date.now(),
                },
              };

              fields.push(field);
            }
          });
        } catch (pageError) {
          console.warn(`Error processing page ${pageNum}:`, pageError);
        }
      }

      setFormFields(fields);
      onFormFieldsChange(fields);

      // Initialize form data
      const initialData: FormData = {};
      fields.forEach((field) => {
        initialData[field.name] =
          field.value || field.defaultValue || getDefaultValue(field.type);
      });
      setFormData(initialData);
      onFormDataChange(initialData);

      toast(
        `Form fields extracted: Found ${fields.length} form fields across ${numPages} pages`
      );
    } catch (error) {
      console.error("Error extracting form fields:", error);
      toast("Form extraction failed: Unable to extract form fields from PDF");
    } finally {
      setIsLoading(false);
    }
  }, [pdfDocument, numPages, onFormFieldsChange, onFormDataChange]);

  const getFieldType = (annotation: { fieldType?: string; multiLine?: boolean; checkBox?: boolean; radioButton?: boolean; combo?: boolean; list?: boolean; signature?: boolean }): FormFieldType => {
    if (annotation.fieldType === "Tx") {
      return annotation.multiLine ? "textarea" : "text";
    }
    if (annotation.fieldType === "Btn") {
      return annotation.checkBox ? "checkbox" : "radio";
    }
    if (annotation.fieldType === "Ch") {
      return "select";
    }
    if (annotation.fieldType === "Sig") {
      return "signature";
    }
    return "text";
  };

  const extractValidation = (): FormFieldValidation => {
    const validation: FormFieldValidation = {
      isValid: true,
      errors: [],
    };

    // Note: These properties would need to be added to FormFieldValidation interface
    // if (annotation.maxLen) validation.maxLength = annotation.maxLen;
    // if (annotation.format?.type === "number") {
    //   validation.min = annotation.format.min;
    //   validation.max = annotation.format.max;
    // }

    return validation;
  };

  const getDefaultValue = (type: FormFieldType) => {
    switch (type) {
      case "checkbox":
        return false;
      case "number":
        return 0;
      case "select":
        return "";
      default:
        return "";
    }
  };

  // Form field validation
  const validateField = (field: FormField, value: unknown): string | null => {
    if (field.required && (!value || value === "")) {
      return "This field is required";
    }

    if (field.validation) {
      const { pattern, minLength, maxLength, min, max } = field.validation;

      if (pattern && typeof value === "string") {
        const regex = new RegExp(pattern);
        if (!regex.test(value)) {
          return "Invalid format";
        }
      }

      if (minLength && typeof value === "string" && value.length < minLength) {
        return `Minimum length is ${minLength} characters`;
      }

      if (maxLength && typeof value === "string" && value.length > maxLength) {
        return `Maximum length is ${maxLength} characters`;
      }

      if (min !== undefined && typeof value === "number" && value < min) {
        return `Minimum value is ${min}`;
      }

      if (max !== undefined && typeof value === "number" && value > max) {
        return `Maximum value is ${max}`;
      }
    }

    // Type-specific validation
    if (field.type === "email" && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return "Invalid email address";
      }
    }

    return null;
  };

  // Enhanced form validation
  const validateAllFields = useCallback((): FormValidationResult => {
    const errors: Record<string, string> = {};
    const fieldValidations: Record<string, FormFieldValidation> = {};
    let totalErrors = 0;
    let totalWarnings = 0;
    let validFields = 0;
    let filledRequiredFields = 0;
    const requiredFields = formFields.filter((f) => f.required).length;

    formFields.forEach((field) => {
      const fieldErrors: string[] = [];
      const fieldWarnings: string[] = [];
      const value = formData[field.name];

      // Required validation
      if (field.required && (!value || value === "")) {
        fieldErrors.push("This field is required");
      } else if (field.required && value) {
        filledRequiredFields++;
      }

      // Type-specific validation
      const typeError = validateField(field, value);
      if (typeError) {
        fieldErrors.push(typeError);
      }

      // Custom validation rules
      if (field.validationRules) {
        field.validationRules.forEach((rule) => {
          const ruleResult = validateRule(rule, value);
          if (!ruleResult.isValid) {
            if (rule.severity === "error") {
              fieldErrors.push(ruleResult.message || rule.message);
            } else {
              fieldWarnings.push(ruleResult.message || rule.message);
            }
          }
        });
      }

      // Custom validator
      if (field.customValidator) {
        const customResult = field.customValidator(value);
        if (!customResult.isValid) {
          if (customResult.severity === "error") {
            fieldErrors.push(
              customResult.message || "Custom validation failed"
            );
          } else {
            fieldWarnings.push(
              customResult.message || "Custom validation warning"
            );
          }
        }
      }

      fieldValidations[field.id] = {
        isValid: fieldErrors.length === 0,
        errors: fieldErrors,
        warnings: fieldWarnings,
      };

      if (fieldErrors.length === 0) validFields++;
      totalErrors += fieldErrors.length;
      totalWarnings += fieldWarnings.length;

      if (fieldErrors.length > 0) {
        errors[field.name] = fieldErrors.join(", ");
      }
    });

    const result: FormValidationResult = {
      isValid: totalErrors === 0,
      fields: fieldValidations,
      summary: {
        totalFields: formFields.length,
        validFields,
        requiredFields,
        filledRequiredFields,
        errors: totalErrors,
        warnings: totalWarnings,
      },
    };

    setValidationErrors(errors);
    setValidationResult(result);
    return result;
  }, [formFields, formData]);

  const validateRule = (rule: ValidationRule, value: unknown): ValidationResult => {
    switch (rule.type) {
      case "required":
        return {
          isValid: value !== undefined && value !== null && value !== "",
        };
      case "minLength":
        return {
          isValid: !value || value.toString().length >= (rule.value || 0),
        };
      case "maxLength":
        return {
          isValid:
            !value || value.toString().length <= (rule.value || Infinity),
        };
      case "pattern":
        if (!value || !rule.value) return { isValid: true };
        const regex = new RegExp(rule.value);
        return { isValid: regex.test(value.toString()) };
      case "email":
        if (!value) return { isValid: true };
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return { isValid: emailRegex.test(value) };
      case "url":
        if (!value) return { isValid: true };
        try {
          new URL(value);
          return { isValid: true };
        } catch {
          return { isValid: false };
        }
      case "number":
        return { isValid: !value || !isNaN(Number(value)) };
      case "date":
        return { isValid: !value || !isNaN(Date.parse(value)) };
      default:
        return { isValid: true };
    }
  };

  // Handle form field value changes
  const handleFieldChange = (fieldName: string, value: unknown) => {
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);
    onFormDataChange(newFormData);

    // Clear validation error for this field
    if (validationErrors[fieldName]) {
      const newErrors = { ...validationErrors };
      delete newErrors[fieldName];
      setValidationErrors(newErrors);
    }

    // Update field modification time
    setFormFields((prev) =>
      prev.map((field) =>
        field.name === fieldName
          ? { ...field, metadata: { ...field.metadata, modified: Date.now() } }
          : field
      )
    );
  };

  // Save form data
  const saveFormData = () => {
    if (!validateAllFields()) {
      toast("Validation failed: Please fix the errors before saving");
      return;
    }

    const formDataWithMetadata = {
      formData,
      metadata: {
        savedAt: new Date().toISOString(),
        version: "1.0",
        fieldCount: formFields.length,
      },
    };

    localStorage.setItem("pdf-form-data", JSON.stringify(formDataWithMetadata));

    toast("Form saved: Form data has been saved locally");
  };

  // Load form data
  const loadFormData = () => {
    try {
      const saved = localStorage.getItem("pdf-form-data");
      if (saved) {
        const parsed = JSON.parse(saved);
        setFormData(parsed.formData || {});
        onFormDataChange(parsed.formData || {});

        toast("Form loaded: Previously saved form data has been loaded");
      }
    } catch {
      toast("Load failed: Unable to load saved form data");
    }
  };

  // Enhanced template management
  const applyTemplate = useCallback(
    (template: FormTemplate) => {
      const templateFields: FormField[] = template.fields.map(
        (templateField, index) => ({
          ...templateField,
          id: `template-${Date.now()}-${index}`,
          position: {
            ...templateField.position,
            pageNumber: currentPage,
            x: templateField.position?.x || 100,
            y: templateField.position?.y || 100 + index * 50,
          },
        })
      );

      const updatedFields = [...formFields, ...templateFields];
      setFormFields(updatedFields);
      onFormFieldsChange(updatedFields);

      // Update template usage count
      if (onTemplateApply) {
        onTemplateApply(template);
      }

      toast(
        `Applied template: ${template.name} (${templateFields.length} fields added)`
      );
    },
    [formFields, currentPage, onFormFieldsChange, onTemplateApply]
  );

  const saveAsTemplate = useCallback(
    (name: string, description: string) => {
      if (formFields.length === 0) {
        toast("No fields to save as template");
        return;
      }



      if (onTemplateSave) {
        onTemplateSave(name, description);
      }

      toast(`Template saved: ${name}`);
    },
    [formFields, onTemplateSave]
  );

  // Enhanced form submission
  const handleFormSubmit = useCallback(() => {
    const validation = validateAllFields();

    if (validation.isValid) {
      if (onFormSubmit) {
        onFormSubmit(formData, validation);
      } else {
        toast("Form submitted successfully!");
      }
    } else {
      toast(
        `Form has ${validation.summary.errors} errors that need to be fixed`
      );
    }
  }, [formData, validateAllFields]);

  // Enhanced export functionality
  const exportFormData = useCallback(
    (format: "json" | "fdf" | "xfdf" | "csv") => {
      if (onFormExport) {
        onFormExport(format);
        return;
      }

      // Default export implementation
      if (format === "json") {
        const exportData = {
          formData,
          fields: formFields.map((field) => ({
            name: field.name,
            type: field.type,
            required: field.required,
            page: field.position.pageNumber,
            value: formData[field.name],
          })),
          validation: validationResult,
          exportedAt: new Date().toISOString(),
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: "application/json",
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "form-data.json";
        link.click();
        URL.revokeObjectURL(url);
      } else if (format === "csv") {
        const headers = ["Field Name", "Value", "Type", "Page", "Required"];
        const rows = formFields.map((field) => [
          field.name,
          formData[field.name] || "",
          field.type,
          field.position.pageNumber.toString(),
          field.required ? "Yes" : "No",
        ]);

        const csvContent = [
          headers.join(","),
          ...rows.map((row) => row.join(",")),
        ].join("\n");
        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "form-data.csv";
        link.click();
        URL.revokeObjectURL(url);
      }

      toast(`Export successful: Form data exported as ${format.toUpperCase()}`);
    },
    [formData, formFields, validationResult, onFormExport]
  );

  // Reset form
  const resetForm = () => {
    const resetData: FormData = {};
    formFields.forEach((field) => {
      resetData[field.name] = field.defaultValue || getDefaultValue(field.type);
    });
    setFormData(resetData);
    onFormDataChange(resetData);
    setValidationErrors({});

    toast("Form reset: All fields have been reset to default values");
  };

  // Enhanced field rendering with more types
  const renderFormField = (field: FormField) => {
    const value = formData[field.name] || "";
    const error = validationErrors[field.name];
    const fieldValidation = validationResult?.fields[field.id];
    const hasWarnings =
      fieldValidation?.warnings && fieldValidation.warnings.length > 0;
    const isCurrentPage = field.position.pageNumber === currentPage;

    const fieldProps = {
      disabled: field.readonly || field.disabled,
      className: cn(
        error ? "border-red-500" : "",
        hasWarnings ? "border-yellow-500" : "",
        field.isSelected ? "ring-2 ring-primary" : ""
      ),
    };

    let input: React.ReactNode;

    switch (field.type) {
      case "text":
      case "email":
      case "phone":
      case "url":
        input = (
          <Input
            type={
              field.type === "phone"
                ? "tel"
                : field.type === "url"
                ? "url"
                : field.type
            }
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.metadata.placeholder}
            maxLength={field.maxLength}
            minLength={field.minLength}
            pattern={field.pattern}
            autoComplete={field.autoComplete}
            autoFocus={field.autoFocus}
            tabIndex={field.tabIndex}
            {...fieldProps}
          />
        );
        break;

      case "textarea":
      case "multiline":
        input = (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.metadata.placeholder}
            maxLength={field.maxLength}
            minLength={field.minLength}
            {...fieldProps}
          />
        );
        break;

      case "number":
        input = (
          <Input
            type="number"
            value={value}
            onChange={(e) =>
              handleFieldChange(field.name, Number(e.target.value))
            }
            min={field.min || field.validation?.min}
            max={field.max || field.validation?.max}
            step={field.step}
            {...fieldProps}
          />
        );
        break;

      case "checkbox":
        input = (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={Boolean(value)}
              onCheckedChange={(checked) =>
                handleFieldChange(field.name, checked)
              }
              disabled={field.readonly || field.disabled}
            />
            {field.label && (
              <label className="text-sm font-medium">{field.label}</label>
            )}
          </div>
        );
        break;

      case "radio":
        input = (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={field.name}
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) =>
                    handleFieldChange(field.name, e.target.value)
                  }
                  disabled={field.readonly || field.disabled || option.disabled}
                  className="w-4 h-4"
                />
                <label className="text-sm">{option.label}</label>
              </div>
            ))}
          </div>
        );
        break;

      case "select":
      case "dropdown":
        input = (
          <Select
            value={value}
            onValueChange={(newValue) =>
              handleFieldChange(field.name, newValue)
            }
            disabled={field.readonly || field.disabled}
          >
            <SelectTrigger className={error ? "border-red-500" : ""}>
              <SelectValue
                placeholder={field.metadata.placeholder || "Select an option"}
              />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
        break;

      case "listbox":
        input = (
          <div className="border rounded-md p-2 max-h-32 overflow-y-auto">
            {field.options?.map((option) => (
              <div
                key={option.value}
                className="flex items-center space-x-2 p-1"
              >
                <input
                  type="checkbox"
                  checked={
                    Array.isArray(value)
                      ? value.includes(option.value)
                      : value === option.value
                  }
                  onChange={(e) => {
                    if (field.multiSelect) {
                      const currentValues = Array.isArray(value) ? value : [];
                      const newValues = e.target.checked
                        ? [...currentValues, option.value]
                        : currentValues.filter((v) => v !== option.value);
                      handleFieldChange(field.name, newValues);
                    } else {
                      handleFieldChange(
                        field.name,
                        e.target.checked ? option.value : ""
                      );
                    }
                  }}
                  disabled={field.readonly || field.disabled || option.disabled}
                  className="w-4 h-4"
                />
                <label className="text-sm">{option.label}</label>
              </div>
            ))}
          </div>
        );
        break;

      case "date":
        input = (
          <Input
            type="date"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            {...fieldProps}
          />
        );
        break;

      case "file":
        input = (
          <Input
            type="file"
            onChange={(e) => {
              const file = e.target.files?.[0];
              handleFieldChange(field.name, file);
            }}
            {...fieldProps}
          />
        );
        break;

      case "image":
        input = (
          <div className="space-y-2">
            <Input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (e) => {
                    handleFieldChange(field.name, e.target?.result);
                  };
                  reader.readAsDataURL(file);
                }
              }}
              {...fieldProps}
            />
            {value && (
              <div className="border rounded-lg p-2">
                <img
                  src={value}
                  alt="Uploaded image"
                  className="max-w-full h-auto max-h-32 object-contain"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFieldChange(field.name, null)}
                  className="mt-2"
                >
                  Remove Image
                </Button>
              </div>
            )}
          </div>
        );
        break;

      case "barcode":
        input = (
          <div className="space-y-2">
            <Input
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder="Enter barcode data"
              {...fieldProps}
            />
            {value && (
              <div className="border rounded-lg p-4 text-center bg-white">
                <div className="font-mono text-sm border-2 border-black inline-block px-2 py-1">
                  {value}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Barcode representation
                </div>
              </div>
            )}
          </div>
        );
        break;

      case "signature":
        input = (
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center">
            <div className="text-sm text-muted-foreground">
              {value ? "Signature captured" : "Click to add signature"}
            </div>
            {value && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFieldChange(field.name, null)}
                className="mt-2"
              >
                Clear Signature
              </Button>
            )}
          </div>
        );
        break;

      case "button":
      case "submit":
      case "reset":
        input = (
          <Button
            type={
              field.type === "submit"
                ? "submit"
                : field.type === "reset"
                ? "reset"
                : "button"
            }
            onClick={() => {
              if (field.type === "submit") {
                handleFormSubmit();
              } else if (field.type === "reset") {
                resetForm();
              }
            }}
            disabled={field.disabled}
            className="w-full"
          >
            {field.label || field.name}
          </Button>
        );
        break;

      default:
        input = (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            {...fieldProps}
          />
        );
    }

    return (
      <div
        key={field.id}
        className={cn(
          "space-y-2 p-3 rounded-lg border transition-all",
          isCurrentPage ? "bg-primary/5 border-primary/20" : "bg-muted/30",
          field.isSelected ? "ring-2 ring-primary" : "",
          error ? "border-red-500" : "",
          hasWarnings ? "border-yellow-500" : ""
        )}
        onClick={() => {
          if (isDesignMode) {
            setFormFields((prev) =>
              prev.map((f) => ({
                ...f,
                isSelected: f.id === field.id,
              }))
            );
          }
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">
              {field.label || field.name}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Badge variant="outline" className="text-xs">
              {field.type}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Page {field.position.pageNumber}
            </Badge>
            {field.metadata?.group && (
              <Badge variant="outline" className="text-xs">
                {field.metadata.group}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1">
            {field.readonly && (
              <Lock className="h-3 w-3 text-muted-foreground" />
            )}
            {field.disabled && (
              <XCircle className="h-3 w-3 text-muted-foreground" />
            )}
            {fieldValidation?.isValid === false && (
              <XCircle className="h-3 w-3 text-red-500" />
            )}
            {fieldValidation?.isValid === true && (
              <CheckCircle className="h-3 w-3 text-green-500" />
            )}
            {hasWarnings && (
              <AlertTriangle className="h-3 w-3 text-yellow-500" />
            )}
          </div>
        </div>

        {input}

        {error && showValidation && (
          <div className="flex items-center gap-1 text-sm text-red-600">
            <AlertCircle className="h-3 w-3" />
            {error}
          </div>
        )}

        {hasWarnings && showValidation && (
          <div className="flex items-center gap-1 text-sm text-yellow-600">
            <AlertTriangle className="h-3 w-3" />
            {fieldValidation?.warnings?.join(", ")}
          </div>
        )}

        {field.metadata.tooltip && (
          <div className="text-xs text-muted-foreground">
            💡 {field.metadata.tooltip}
          </div>
        )}

        {field.metadata.helpText && (
          <div className="text-xs text-muted-foreground">
            ℹ️ {field.metadata.helpText}
          </div>
        )}
      </div>
    );
  };

  // Auto-extract form fields when PDF loads
  useEffect(() => {
    if (pdfDocument) {
      extractFormFields();
    }
  }, [pdfDocument, extractFormFields]);

  // Auto-validate when form data changes
  useEffect(() => {
    if (formFields.length > 0) {
      validateAllFields();
    }
  }, [formData, formFields, validateAllFields]);

  // Sync design mode
  useEffect(() => {
    setFormMode(isDesignMode ? "design" : mode === "view" ? "view" : "edit");
  }, [isDesignMode, mode]);

  // Form field toolbar for design mode
  const FormFieldToolbar = () => {
    const fieldTypes: Array<{
      type: FormFieldType;
      icon: React.ComponentType<{ className?: string }>;
      label: string;
      category: "input" | "selection" | "action" | "special";
    }> = [
      { type: "text", icon: Type, label: "Text", category: "input" },
      {
        type: "multiline",
        icon: FileText,
        label: "Text Area",
        category: "input",
      },
      { type: "number", icon: Hash, label: "Number", category: "input" },
      { type: "email", icon: AtSign, label: "Email", category: "input" },
      { type: "phone", icon: Phone, label: "Phone", category: "input" },
      { type: "url", icon: Link, label: "URL", category: "input" },
      { type: "date", icon: Calendar, label: "Date", category: "input" },
      {
        type: "checkbox",
        icon: CheckSquare,
        label: "Checkbox",
        category: "selection",
      },
      { type: "radio", icon: Circle, label: "Radio", category: "selection" },
      {
        type: "dropdown",
        icon: List,
        label: "Dropdown",
        category: "selection",
      },
      { type: "button", icon: Square, label: "Button", category: "action" },
      {
        type: "signature",
        icon: FileText,
        label: "Signature",
        category: "special",
      },
    ];

    const [activeCategory, setActiveCategory] = useState<string>("input");
    const categories = ["input", "selection", "action", "special"];
    const filteredFields = fieldTypes.filter(
      (field) => field.category === activeCategory
    );

    if (formMode !== "design") return null;

    return (
      <Card className="p-3 space-y-3 mb-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Form Fields</h4>
          <Badge variant="secondary">Design Mode</Badge>
        </div>

        {/* Categories */}
        <div className="flex gap-1 overflow-x-auto">
          {categories.map((category) => (
            <Button
              key={category}
              variant={activeCategory === category ? "default" : "ghost"}
              size="sm"
              onClick={() => setActiveCategory(category)}
              className="text-xs capitalize whitespace-nowrap"
            >
              {category}
            </Button>
          ))}
        </div>

        <Separator />

        {/* Field Types */}
        <div className="grid grid-cols-3 gap-2">
          {filteredFields.map((field) => (
            <Button
              key={field.type}
              variant={selectedFieldType === field.type ? "default" : "ghost"}
              size="sm"
              onClick={() => {
                setSelectedFieldType(field.type);
                const newField: FormField = {
                  id: `field_${Date.now()}`,
                  name: `field_${Date.now()}`,
                  type: field.type,
                  label: `New ${field.label}`,
                  value: getDefaultValue(field.type),
                  required: false,
                  readonly: false,
                  position: {
                    pageNumber: currentPage,
                    x: 100,
                    y: 100,
                    width: 200,
                    height: field.type === "multiline" ? 80 : 30,
                  },
                  appearance: {
                    fontSize: 12,
                    fontColor: "#000000",
                    backgroundColor: "#ffffff",
                    borderColor: "#cccccc",
                    borderWidth: 1,
                  },
                  metadata: {
                    created: Date.now(),
                    modified: Date.now(),
                  },
                };

                const updatedFields = [...formFields, newField];
                setFormFields(updatedFields);
                onFormFieldsChange(updatedFields);

                if (onFieldAdd) {
                  onFieldAdd(newField);
                }
              }}
              className="flex flex-col h-auto p-2 gap-1"
              title={field.label}
            >
              <field.icon className="size-4" />
              <span className="text-xs">{field.label}</span>
            </Button>
          ))}
        </div>
      </Card>
    );
  };

  const currentPageFields = formFields.filter(
    (field) => field.position.pageNumber === currentPage
  );

  const formStats = useMemo(() => {
    const totalFields = formFields.length;
    const filledFields = formFields.filter((field) => {
      const value = formData[field.name];
      return (
        value !== "" && value !== null && value !== undefined && value !== false
      );
    }).length;
    const requiredFields = formFields.filter((field) => field.required).length;
    const filledRequiredFields = formFields.filter((field) => {
      if (!field.required) return false;
      const value = formData[field.name];
      return (
        value !== "" && value !== null && value !== undefined && value !== false
      );
    }).length;

    return {
      totalFields,
      filledFields,
      requiredFields,
      filledRequiredFields,
      completionPercentage:
        totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0,
      isComplete: requiredFields === filledRequiredFields,
    };
  }, [formFields, formData]);

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Enhanced Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                PDF Forms
              </CardTitle>
              <CardDescription>
                {isDesignMode
                  ? "Design and manage form fields"
                  : "Fill and manage interactive form fields"}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {onDesignModeToggle && (
                <Button
                  variant={isDesignMode ? "default" : "outline"}
                  size="sm"
                  onClick={onDesignModeToggle}
                >
                  <Settings className="h-4 w-4 mr-1" />
                  {isDesignMode ? "Exit Design" : "Design Mode"}
                </Button>
              )}
              <Button
                variant={showValidation ? "default" : "outline"}
                size="sm"
                onClick={() => setShowValidation(!showValidation)}
              >
                {showValidation ? (
                  <Eye className="h-4 w-4" />
                ) : (
                  <EyeOff className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Enhanced Form Statistics */}
          <div className="grid grid-cols-4 gap-4 p-3 bg-muted/50 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-bold">{formStats.totalFields}</div>
              <div className="text-xs text-muted-foreground">Total Fields</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">
                {formStats.filledFields}
              </div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">
                {formStats.completionPercentage}%
              </div>
              <div className="text-xs text-muted-foreground">Progress</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center">
                {validationResult?.isValid ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : validationResult ? (
                  <XCircle className="h-5 w-5 text-red-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-gray-400" />
                )}
              </div>
              <div className="text-xs text-muted-foreground">Status</div>
            </div>
          </div>

          {/* Enhanced Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Form Completion</span>
              <span>{formStats.completionPercentage}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all"
                style={{ width: `${formStats.completionPercentage}%` }}
              />
            </div>
            {formStats.requiredFields > 0 && (
              <>
                <div className="flex justify-between text-sm">
                  <span>Required Fields</span>
                  <span>
                    {formStats.filledRequiredFields}/{formStats.requiredFields}
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all"
                    style={{
                      width: `${
                        (formStats.filledRequiredFields /
                          Math.max(formStats.requiredFields, 1)) *
                        100
                      }%`,
                    }}
                  />
                </div>
              </>
            )}
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button size="sm" onClick={saveFormData}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={loadFormData}>
              <Upload className="h-4 w-4 mr-2" />
              Load
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => exportFormData("json")}
            >
              <Download className="h-4 w-4 mr-2" />
              Export JSON
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => exportFormData("csv")}
            >
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
            <Button size="sm" variant="outline" onClick={resetForm}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            {onFormSubmit && (
              <Button
                size="sm"
                onClick={handleFormSubmit}
                disabled={validationResult ? !validationResult.isValid : false}
              >
                Submit Form
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Panel Tabs */}
      <div className="flex gap-1">
        {["fields", "validation", "templates"].map((panel) => (
          <Button
            key={panel}
            variant={activePanel === panel ? "default" : "ghost"}
            size="sm"
            onClick={() => setActivePanel(panel as "fields" | "validation" | "templates")}
            className="capitalize"
          >
            {panel}
            {panel === "validation" &&
              validationResult &&
              !validationResult.isValid && (
                <Badge
                  variant="destructive"
                  className="ml-2 h-4 w-4 p-0 text-xs"
                >
                  {validationResult.summary.errors}
                </Badge>
              )}
            {panel === "templates" && (
              <Badge variant="secondary" className="ml-2 h-4 w-4 p-0 text-xs">
                {templates.length}
              </Badge>
            )}
          </Button>
        ))}
      </div>

      {/* Panel Content */}
      <Card className="flex-1 overflow-hidden">
        <CardContent className="h-full p-4">
          {activePanel === "validation" && validationResult && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Form Validation</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => validateAllFields()}
                >
                  <Zap className="size-4 mr-1" />
                  Validate
                </Button>
              </div>

              {/* Validation Summary */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">
                    Completion
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all"
                        style={{
                          width: `${
                            (validationResult.summary.filledRequiredFields /
                              Math.max(
                                validationResult.summary.requiredFields,
                                1
                              )) *
                            100
                          }%`,
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium">
                      {Math.round(
                        (validationResult.summary.filledRequiredFields /
                          Math.max(
                            validationResult.summary.requiredFields,
                            1
                          )) *
                          100
                      )}
                      %
                    </span>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Status</div>
                  <div className="flex items-center gap-2">
                    {validationResult.isValid ? (
                      <CheckCircle className="size-4 text-green-500" />
                    ) : (
                      <XCircle className="size-4 text-red-500" />
                    )}
                    <span className="text-sm font-medium">
                      {validationResult.isValid ? "Valid" : "Invalid"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold">
                    {validationResult.summary.totalFields}
                  </div>
                  <div className="text-xs text-muted-foreground">Total</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-green-600">
                    {validationResult.summary.validFields}
                  </div>
                  <div className="text-xs text-muted-foreground">Valid</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-yellow-600">
                    {validationResult.summary.warnings}
                  </div>
                  <div className="text-xs text-muted-foreground">Warnings</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-red-600">
                    {validationResult.summary.errors}
                  </div>
                  <div className="text-xs text-muted-foreground">Errors</div>
                </div>
              </div>
            </div>
          )}

          {activePanel === "templates" && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Form Templates</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const name = prompt("Template name:");
                    const description = prompt("Template description:");
                    if (name) {
                      saveAsTemplate(name, description || "");
                    }
                  }}
                >
                  <Plus className="size-4 mr-1" />
                  Save Template
                </Button>
              </div>

              <ScrollArea className="h-60">
                <div className="space-y-2">
                  {templates.length > 0 ? (
                    templates.map((template) => (
                      <Card
                        key={template.id}
                        className="p-3 cursor-pointer hover:bg-accent transition-colors"
                        onClick={() => applyTemplate(template)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">
                              {template.name}
                            </div>
                            <div className="text-sm text-muted-foreground truncate">
                              {template.description}
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {template.category}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {template.fields.length} fields
                              </span>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      <FileText className="size-8 mx-auto mb-2 opacity-50" />
                      <p>No templates available</p>
                      <p className="text-sm">
                        Save your current form as a template
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          )}

          {activePanel === "fields" && (
            <>
              {/* Form Field Toolbar for Design Mode */}
              <FormFieldToolbar />

              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-2">Extracting form fields...</span>
                </div>
              ) : formFields.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium text-lg mb-2">
                    No Form Fields Found
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    {formMode === "design"
                      ? "Start by adding form fields using the toolbar above."
                      : "This PDF doesn't contain interactive form fields."}
                  </p>
                  {formMode !== "design" && (
                    <Button onClick={extractFormFields}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Re-scan Document
                    </Button>
                  )}
                </div>
              ) : (
                <ScrollArea className="h-full">
                  <div className="space-y-4">
                    {/* Current Page Fields */}
                    {currentPageFields.length > 0 && (
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <h4 className="font-medium">Current Page Fields</h4>
                          <Badge variant="default">
                            {currentPageFields.length}
                          </Badge>
                        </div>
                        <div className="space-y-3">
                          {currentPageFields.map(renderFormField)}
                        </div>
                        <Separator className="my-4" />
                      </div>
                    )}

                    {/* All Fields */}
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <h4 className="font-medium">All Form Fields</h4>
                        <Badge variant="outline">{formFields.length}</Badge>
                      </div>
                      <div className="space-y-3">
                        {formFields.map(renderFormField)}
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Validation Summary */}
      {Object.keys(validationErrors).length > 0 && showValidation && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-950">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-300 mb-2">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">
                Validation Errors ({Object.keys(validationErrors).length})
              </span>
            </div>
            <div className="space-y-1 text-sm">
              {Object.entries(validationErrors).map(([fieldName, error]) => (
                <div key={fieldName} className="text-red-600 dark:text-red-400">
                  <strong>{fieldName}:</strong> {error}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Validation Summary for Warnings */}
      {validationResult &&
        validationResult.summary.warnings > 0 &&
        showValidation && (
          <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300 mb-2">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">
                  Validation Warnings ({validationResult.summary.warnings})
                </span>
              </div>
              <div className="space-y-1 text-sm">
                {Object.entries(validationResult.fields).map(
                  ([fieldId, fieldValidation]) =>
                    fieldValidation.warnings?.map((warning, index) => (
                      <div
                        key={`${fieldId}-${index}`}
                        className="text-yellow-600 dark:text-yellow-400"
                      >
                        <strong>Field {fieldId}:</strong> {warning}
                      </div>
                    ))
                )}
              </div>
            </CardContent>
          </Card>
        )}
    </div>
  );
}
