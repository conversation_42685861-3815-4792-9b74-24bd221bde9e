import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import PDFSimplePage from '@/components/core/pdf-simple-page'

// Mock the Page component from react-pdf
vi.mock('react-pdf', () => ({
  Page: ({ pageNumber, scale, rotate, loading, className, renderTextLayer, renderAnnotationLayer, onLoadSuccess, customTextRenderer }: any) => (
    <div 
      data-testid="pdf-page" 
      data-scale={scale?.toString()} 
      data-rotate={rotate?.toString()} 
      className={className}
    >
      Page {pageNumber}
    </div>
  )
}))

// Mock the child components
vi.mock('@/components/annotations/pdf-annotation-overlay', () => ({
  default: ({ onAnnotationAdd, annotations }: any) => (
    <div data-testid="annotation-overlay">
      <button onClick={() => onAnnotationAdd?.({ type: 'highlight', content: 'test', pageNumber: 1, x: 0, y: 0, color: '#FFEB3B' })}>
        Add Annotation
      </button>
      <div data-testid="annotations-count">{annotations?.length || 0}</div>
    </div>
  ),
}))

vi.mock('@/components/forms/pdf-form-overlay', () => ({
  default: ({ formFields, onFormDataChange }: any) => (
    <div data-testid="form-overlay">
      <button onClick={() => onFormDataChange?.({ field1: 'value1' })}>
        Update Form
      </button>
      <div data-testid="form-fields-count">{formFields?.length || 0}</div>
    </div>
  ),
}))

vi.mock('@/components/tools/pdf-text-selection', () => ({
  default: ({ onTextSelected }: any) => (
    <div data-testid="text-selection">
      <button onClick={() => onTextSelected?.({
        id: 'selection-123',
        text: 'selected text',
        pageNumber: 1,
        boundingRect: { x: 0, y: 0, width: 100, height: 20 },
        startOffset: 0,
        endOffset: 12,
        context: 'selected text',
        timestamp: new Date()
      })}>
        Select Text
      </button>
    </div>
  ),
}))

describe('PDFSimplePage - Consolidated Component', () => {
  const defaultProps = {
    pageNumber: 1,
    scale: 1.0,
    rotation: 0,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Functionality (Original Interface)', () => {
    it('renders with basic props', () => {
      render(<PDFSimplePage {...defaultProps} />)
      
      expect(screen.getByTestId('pdf-page')).toBeInTheDocument()
    })

    it('applies custom className', () => {
      render(<PDFSimplePage {...defaultProps} className="custom-class" />)
      
      const pageElement = screen.getByTestId('pdf-page')
      expect(pageElement).toHaveClass('custom-class')
    })

    it('passes scale and rotation to PDF Page component', () => {
      render(<PDFSimplePage {...defaultProps} scale={1.5} rotation={90} />)
      
      const pageElement = screen.getByTestId('pdf-page')
      expect(pageElement).toHaveAttribute('data-scale', '1.5')
      expect(pageElement).toHaveAttribute('data-rotate', '90')
    })
  })

  describe('Enhanced Functionality - Feature Toggles', () => {
    it('enables annotations when enableAnnotations is true', () => {
      const onAnnotationAdd = vi.fn()
      render(
        <PDFSimplePage 
          {...defaultProps} 
          enableAnnotations={true}
          onAnnotationAdd={onAnnotationAdd}
        />
      )
      
      expect(screen.getByTestId('annotation-overlay')).toBeInTheDocument()
      
      fireEvent.click(screen.getByText('Add Annotation'))
      expect(onAnnotationAdd).toHaveBeenCalledWith({ type: 'highlight', content: 'test', pageNumber: 1, x: 0, y: 0, color: '#FFEB3B' })
    })

    it('does not render annotations when enableAnnotations is false', () => {
      render(<PDFSimplePage {...defaultProps} enableAnnotations={false} />)
      
      expect(screen.queryByTestId('annotation-overlay')).not.toBeInTheDocument()
    })

    it('enables forms when enableForms is true', () => {
      const onFormDataChange = vi.fn()
      render(
        <PDFSimplePage 
          {...defaultProps} 
          enableForms={true}
          onFormDataChange={onFormDataChange}
        />
      )
      
      expect(screen.getByTestId('form-overlay')).toBeInTheDocument()
      
      fireEvent.click(screen.getByText('Update Form'))
      expect(onFormDataChange).toHaveBeenCalledWith({ field1: 'value1' })
    })

    it('enables text selection when enableTextSelection is true', () => {
      const onTextSelected = vi.fn()
      render(
        <PDFSimplePage 
          {...defaultProps} 
          enableTextSelection={true}
          onTextSelected={onTextSelected}
        />
      )
      
      expect(screen.getByTestId('text-selection')).toBeInTheDocument()
      
      fireEvent.click(screen.getByText('Select Text'))
      expect(onTextSelected).toHaveBeenCalledWith({
        id: 'selection-123',
        text: 'selected text',
        pageNumber: 1,
        boundingRect: { x: 0, y: 0, width: 100, height: 20 },
        startOffset: 0,
        endOffset: 12,
        context: 'selected text',
        timestamp: expect.any(Date)
      })
    })
  })

  describe('Search Integration', () => {
    it('handles search functionality', () => {
      const searchResults = [
        { pageIndex: 0, textItems: [{ str: 'test', matches: [] }] }
      ]
      
      render(
        <PDFSimplePage 
          {...defaultProps} 
          searchText="test"
          searchResults={searchResults}
          currentSearchPageIndex={0}
        />
      )
      
      // Component should render without errors with search props
      expect(screen.getByTestId('pdf-page')).toBeInTheDocument()
    })

    it('handles search options', () => {
      const searchOptions = { caseSensitive: true, wholeWords: false }
      
      render(
        <PDFSimplePage 
          {...defaultProps} 
          searchText="test"
          searchOptions={searchOptions}
        />
      )
      
      expect(screen.getByTestId('pdf-page')).toBeInTheDocument()
    })
  })

  describe('Backward Compatibility', () => {
    it('maintains original simple page behavior by default', () => {
      render(<PDFSimplePage {...defaultProps} />)
      
      // Should render basic page without enhanced features
      expect(screen.getByTestId('pdf-page')).toBeInTheDocument()
      expect(screen.queryByTestId('annotation-overlay')).not.toBeInTheDocument()
      expect(screen.queryByTestId('form-overlay')).not.toBeInTheDocument()
      expect(screen.queryByTestId('text-selection')).not.toBeInTheDocument()
    })

    it('works with minimal props (original interface)', () => {
      render(
        <PDFSimplePage 
          pageNumber={1}
          scale={1.0}
          rotation={0}
        />
      )
      
      expect(screen.getByTestId('pdf-page')).toBeInTheDocument()
    })
  })

  describe('Event Handlers', () => {
    it('calls onSearch when search is triggered', () => {
      const onSearch = vi.fn()
      render(
        <PDFSimplePage 
          {...defaultProps} 
          onSearch={onSearch}
          enableSearch={true}
        />
      )
      
      // Simulate search trigger (implementation specific)
      // This would depend on how search is triggered in the actual component
    })

    it('calls onBookmark when bookmark is triggered', () => {
      const onBookmark = vi.fn()
      render(
        <PDFSimplePage 
          {...defaultProps} 
          onBookmark={onBookmark}
        />
      )
      
      // Test bookmark functionality if exposed
    })
  })

  describe('Props Integration', () => {
    it('passes annotations to annotation overlay', () => {
      const annotations = [
        { 
          id: '1', 
          type: 'highlight', 
          content: 'test annotation', 
          pageNumber: 1,
          x: 100,
          y: 100,
          width: 200,
          height: 20,
          color: '#FFEB3B',
          author: 'Test User',
          createdAt: new Date(),
          updatedAt: new Date(),
          // Include timestamp for backward compatibility
          timestamp: Date.now()
        }
      ]
      
      render(
        <PDFSimplePage 
          {...defaultProps} 
          enableAnnotations={true}
          annotations={annotations}
        />
      )
      
      expect(screen.getByTestId('annotations-count')).toHaveTextContent('1')
    })

    it('passes form fields to form overlay', () => {
      const formFields = [
        { 
          id: '1', 
          type: 'text', 
          name: 'field1', 
          value: '',
          required: false,
          readonly: false,
          position: { 
            pageNumber: 1, 
            x: 0, 
            y: 0, 
            width: 100, 
            height: 20 
          },
          appearance: {
            fontSize: 12,
            fontFamily: 'Helvetica',
            color: '#000000',
            backgroundColor: '#FFFFFF'
          },
          metadata: {
            createdAt: Date.now(),
            updatedAt: Date.now(),
            version: '1.0'
          }
        }
      ]
      
      render(
        <PDFSimplePage 
          {...defaultProps} 
          enableForms={true}
          formFields={formFields}
        />
      )
      
      expect(screen.getByTestId('form-fields-count')).toHaveTextContent('1')
    })
  })
})