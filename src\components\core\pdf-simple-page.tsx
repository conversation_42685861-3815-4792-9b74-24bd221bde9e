"use client";

import React from "react";
import { useRef, useState, useCallback, useEffect, useMemo } from "react";
import { Page } from "react-pdf";
import PDFAnnotationOverlay from "../annotations/pdf-annotation-overlay";
import PDFHighlightOverlay from "../annotations/pdf-highlight-overlay";
import PDFTextSelection, {
  type TextSelection,
} from "../tools/pdf-text-selection";
import PDFContextMenu from "../navigation/pdf-context-menu";
import PDFFormOverlay from "../forms/pdf-form-overlay";
import type {
  Annotation,
  AnnotationType,
} from "../annotations/pdf-annotations";
import type { FormField, FormData } from "../forms/pdf-form-manager";

interface PDFSimplePageProps {
  // Core page properties (original simple page interface)
  pageNumber: number;
  scale: number;
  rotation: number;
  className?: string;

  // Enhanced functionality (from enhanced page variants)
  pdfDocument?: unknown;

  // Search functionality
  searchText?: string;
  searchOptions?: { caseSensitive: boolean; wholeWords: boolean };
  currentSearchPageIndex?: number;
  searchResults?: Array<{ pageIndex: number; textItems: unknown[] }>;

  // Annotation functionality
  annotations?: Annotation[];
  selectedTool?: AnnotationType | null;
  selectedColor?: string;
  onAnnotationAdd?: (annotation: Omit<Annotation, "id" | "timestamp">) => void;
  onAnnotationSelect?: (annotation: Annotation) => void;

  // Form functionality
  formFields?: FormField[];
  formData?: FormData;
  onFormDataChange?: (data: FormData) => void;
  isFormDesignMode?: boolean;

  // Text selection and interaction
  textSelectionEnabled?: boolean;
  onTextSelected?: (selection: TextSelection | null) => void;
  onSearch?: (text: string) => void;
  onBookmark?: () => void;
  onHighlight?: () => void;

  // Custom rendering
  customTextRenderer?: (textItem: unknown) => React.ReactNode;

  // Feature toggles (default to simple page behavior)
  enableAnnotations?: boolean;
  enableForms?: boolean;
  enableTextSelection?: boolean;
  enableSearch?: boolean;
  enableContextMenu?: boolean;
}

const PDFSimplePage = React.memo<PDFSimplePageProps>(
  ({
    // Core properties
    pageNumber,
    scale,
    rotation,
    className,
    pdfDocument,

    // Search functionality
    searchText = "",
    searchOptions = { caseSensitive: false, wholeWords: false },
    currentSearchPageIndex = -1,
    searchResults = [],

    // Annotation functionality
    annotations = [],
    selectedTool = null,
    selectedColor = "#FFEB3B",
    onAnnotationAdd,
    onAnnotationSelect,

    // Form functionality
    formFields = [],
    formData = {},
    onFormDataChange,
    isFormDesignMode = false,

    // Text selection and interaction
    textSelectionEnabled = true,
    onTextSelected,
    onSearch,
    onBookmark,
    onHighlight,

    // Custom rendering
    customTextRenderer,

    // Feature toggles (maintain backward compatibility - default to simple behavior)
    enableAnnotations = false,
    enableForms = false,
    enableTextSelection = true,
    enableSearch = false,
    enableContextMenu = false,
  }) => {
    const [selectedText, setSelectedText] = useState<string | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [pageLoaded, setPageLoaded] = useState(false);

    // Memoize computed values for better performance
    const isCurrentSearchPage = useMemo(
      () =>
        enableSearch &&
        searchResults.length > 0 &&
        currentSearchPageIndex >= 0 &&
        searchResults[currentSearchPageIndex]?.pageIndex === pageNumber - 1,
      [enableSearch, searchResults, currentSearchPageIndex, pageNumber]
    );

    const hasSearchResults = useMemo(
      () =>
        enableSearch &&
        searchResults.some((result) => result.pageIndex === pageNumber - 1),
      [enableSearch, searchResults, pageNumber]
    );

    // Filter annotations for this page only
    const pageAnnotations = useMemo(
      () => annotations.filter((ann) => ann.pageNumber === pageNumber),
      [annotations, pageNumber]
    );

    // Filter form fields for this page only
    const pageFormFields = useMemo(
      () =>
        formFields.filter((field) => field.position.pageNumber === pageNumber),
      [formFields, pageNumber]
    );

    // Text selection handler
    const handleTextSelection = useCallback(
      (selection: TextSelection | null) => {
        setCurrentTextSelection(selection);
        setSelectedText(selection?.text || null);
        onTextSelected?.(selection);
      },
      [onTextSelected]
    );

    const handleHighlight = useCallback(() => {
      if (selectedText) {
        onHighlight?.();
      }
    }, [selectedText, onHighlight]);

    // Reset page loaded state when page changes
    useEffect(() => {
      setPageLoaded(false);
    }, [pageNumber]);

    // Enhanced text renderer for search highlighting
    const enhancedTextRenderer = useCallback(
      ({ str, itemIndex }: { str: string; itemIndex: number }): string => {
        // Use custom renderer if provided
        if (customTextRenderer) {
          const result = customTextRenderer({ str, itemIndex });
          return typeof result === "string" ? result : str;
        }

        // Default search highlighting - return original string for now
        // The actual highlighting will be handled by the overlay
        return str;
      },
      [customTextRenderer]
    );

    const pageContent = (
      <div className="relative">
        <Page
          pageNumber={pageNumber}
          scale={scale}
          rotate={rotation}
          loading={
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          }
          className={className}
          renderTextLayer={enableTextSelection || enableSearch}
          renderAnnotationLayer={!enableAnnotations} // Disable default annotations if using custom overlay
          onLoadSuccess={() => setPageLoaded(true)}
          customTextRenderer={enhancedTextRenderer}
        />

        {/* Search Highlight Overlay */}
        {pageLoaded &&
          enableSearch &&
          hasSearchResults &&
          searchText &&
          pdfDocument && (
            <PDFHighlightOverlay
              pdfDocument={pdfDocument}
              pageNumber={pageNumber}
              scale={scale}
              rotation={rotation}
              searchText={searchText}
              searchOptions={searchOptions}
              isCurrentSearchPage={isCurrentSearchPage}
            />
          )}

        {/* Text Selection Handler */}
        {pageLoaded &&
          enableTextSelection &&
          textSelectionEnabled &&
          !selectedTool &&
          pdfDocument && (
            <div
              className="absolute inset-0 pointer-events-none"
              style={{ zIndex: 15 }}
            >
              <PDFTextSelection
                pdfDocument={pdfDocument}
                pageNumber={pageNumber}
                scale={scale}
                rotation={rotation}
                onTextSelected={handleTextSelection}
              />
            </div>
          )}

        {/* Form Overlay */}
        {pageLoaded &&
          enableForms &&
          pageFormFields.length > 0 &&
          onFormDataChange && (
            <PDFFormOverlay
              pageNumber={pageNumber}
              scale={scale}
              rotation={rotation}
              formFields={pageFormFields}
              formData={formData}
              onFormDataChange={onFormDataChange}
              isDesignMode={isFormDesignMode}
            />
          )}

        {/* Annotation Overlay */}
        {pageLoaded && enableAnnotations && onAnnotationAdd && (
          <PDFAnnotationOverlay
            pageNumber={pageNumber}
            scale={scale}
            rotation={rotation}
            annotations={pageAnnotations}
            selectedTool={selectedTool}
            selectedColor={selectedColor}
            onAnnotationAdd={onAnnotationAdd}
            onAnnotationSelect={onAnnotationSelect}
          />
        )}
      </div>
    );

    if (enableContextMenu) {
      return (
        <div ref={containerRef} className="relative inline-block">
          <PDFContextMenu
            selectedText={selectedText || ""}
            pageNumber={pageNumber}
            onSearch={onSearch}
            onBookmark={onBookmark}
            onHighlight={handleHighlight}
          >
            {pageContent}
          </PDFContextMenu>
        </div>
      );
    }

    return (
      <div ref={containerRef} className="relative inline-block">
        {pageContent}
      </div>
    );
  }
);

PDFSimplePage.displayName = 'PDFSimplePage';

export default PDFSimplePage;
