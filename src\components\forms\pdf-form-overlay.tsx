"use client";

import { useState, useRef, useCallback } from "react";
import type { FormField, FormData } from "./pdf-form-manager";

interface PDFFormOverlayProps {
  pageNumber: number;
  scale: number;
  rotation: number;
  formFields: FormField[];
  formData: FormData;
  onFormDataChange: (data: FormData) => void;
  isDesignMode?: boolean;
}

export default function PDFFormOverlay({
  pageNumber,
  scale,
  rotation,
  formFields,
  formData,
  onFormDataChange,
  isDesignMode = false,
}: PDFFormOverlayProps) {
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  const pageFields = formFields.filter(
    (field) => field.position.pageNumber === pageNumber
  );

  const handleFieldChange = useCallback(
    (fieldName: string, value: unknown) => {
      const newFormData = { ...formData, [fieldName]: value };
      onFormDataChange(newFormData);
    },
    [formData, onFormDataChange]
  );

  const renderFormField = (field: FormField) => {
    const value = formData[field.name] || "";
    const isFocused = focusedField === field.id;

    const style = {
      position: "absolute" as const,
      left: `${(field.position.x / 612) * 100}%`, // Assuming standard page width
      top: `${(field.position.y / 792) * 100}%`, // Assuming standard page height
      width: `${(field.position.width / 612) * 100}%`,
      height: `${(field.position.height / 792) * 100}%`,
      transform: `scale(${scale}) rotate(${rotation}deg)`,
      transformOrigin: "top left",
      fontSize: `${field.appearance.fontSize * scale}px`,
      color: field.appearance.fontColor,
      backgroundColor: field.appearance.backgroundColor,
      border: `${field.appearance.borderWidth}px solid ${field.appearance.borderColor}`,
      borderRadius: "2px",
      zIndex: isFocused ? 20 : 10,
    };

    const commonProps = {
      onFocus: () => setFocusedField(field.id),
      onBlur: () => setFocusedField(null),
      disabled: field.readonly,
      title: field.metadata.tooltip,
    };

    switch (field.type) {
      case "text":
      case "email":
        return (
          <input
            key={field.id}
            type={field.type}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.metadata.placeholder}
            style={style}
            className="outline-none resize-none"
            {...commonProps}
          />
        );

      case "textarea":
        return (
          <textarea
            key={field.id}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.metadata.placeholder}
            style={style}
            className="outline-none resize-none"
            {...commonProps}
          />
        );

      case "number":
        return (
          <input
            key={field.id}
            type="number"
            value={value}
            onChange={(e) =>
              handleFieldChange(field.name, Number(e.target.value))
            }
            min={field.validation?.min}
            max={field.validation?.max}
            style={style}
            className="outline-none resize-none"
            {...commonProps}
          />
        );

      case "checkbox":
        return (
          <input
            key={field.id}
            type="checkbox"
            checked={Boolean(value)}
            onChange={(e) => handleFieldChange(field.name, e.target.checked)}
            style={{
              ...style,
              width: "auto",
              height: "auto",
              margin: "2px",
            }}
            {...commonProps}
          />
        );

      case "select":
        return (
          <select
            key={field.id}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            style={style}
            className="outline-none"
            {...commonProps}
          >
            <option value="">Select an option</option>
            {field.options?.map((option) => (
              <option key={typeof option === 'string' ? option : option.value} value={typeof option === 'string' ? option : option.value}>
                {typeof option === 'string' ? option : option.label}
              </option>
            ))}
          </select>
        );

      case "date":
        return (
          <input
            key={field.id}
            type="date"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            style={style}
            className="outline-none resize-none"
            {...commonProps}
          />
        );

      case "signature":
        return (
          <div
            key={field.id}
            style={{
              ...style,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              backgroundColor: value ? "#e8f5e8" : "#f8f8f8",
            }}
            onClick={() => {
              // Open signature dialog
              const signature = prompt("Enter signature text:");
              if (signature) {
                handleFieldChange(field.name, signature);
              }
            }}
            {...commonProps}
          >
            <span style={{ fontSize: `${10 * scale}px`, color: "#666" }}>
              {value || "Click to sign"}
            </span>
          </div>
        );

      default:
        return null;
    }
  };

  if (pageFields.length === 0) {
    return null;
  }

  return (
    <div
      ref={overlayRef}
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 5 }}
    >
      {pageFields.map((field) => (
        <div key={field.id} className="pointer-events-auto">
          {renderFormField(field)}
          {isDesignMode && (
            <div
              className="absolute inset-0 border-2 border-blue-500 border-dashed bg-blue-100/20"
              style={{
                left: `${(field.position.x / 612) * 100}%`,
                top: `${(field.position.y / 792) * 100}%`,
                width: `${(field.position.width / 612) * 100}%`,
                height: `${(field.position.height / 792) * 100}%`,
              }}
            >
              <div className="absolute -top-6 left-0 bg-blue-500 text-white text-xs px-1 rounded">
                {field.name} ({field.type})
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
