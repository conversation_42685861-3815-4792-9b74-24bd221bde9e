"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Printer, FileText, Eye } from "lucide-react";
import { toast } from "sonner";
import type { Annotation } from "../annotations/pdf-annotations";

export interface PrintSettings {
  pageRange: 'all' | 'current' | 'range' | 'selection';
  customRange?: string;
  orientation: 'portrait' | 'landscape';
  paperSize: 'A4' | 'Letter' | 'Legal' | 'A3' | 'Custom';
  margins: { top: number; right: number; bottom: number; left: number };
  scale: number;
  quality: 'draft' | 'normal' | 'high';
  colorMode: 'color' | 'grayscale' | 'monochrome';
  duplex: 'none' | 'short' | 'long';
  copies: number;
  collate: boolean;
  annotations: boolean;
  watermark?: string;
}

interface PDFPrintManagerProps {
  pdfDocument: any;
  numPages: number;
  annotations: Annotation[];
  currentScale: number;
}

type PrintMode =
  | "document-only"
  | "annotations-only"
  | "document-with-annotations";
type PageRange = "all" | "current" | "range" | "annotated";

export default function PDFPrintManager({
  pdfDocument,
  numPages,
  annotations,
  currentScale,
}: PDFPrintManagerProps) {
  // Helper function to safely format annotation date
  const formatAnnotationDate = (annotation: Annotation) => {
    if (annotation.createdAt) {
      return annotation.createdAt.toLocaleString();
    }
    return new Date(annotation.timestamp || Date.now()).toLocaleString();
  };

  const formatAnnotationDateShort = (annotation: Annotation) => {
    if (annotation.createdAt) {
      return annotation.createdAt.toLocaleDateString();
    }
    return new Date(annotation.timestamp || Date.now()).toLocaleDateString();
  };
  const [printMode, setPrintMode] = useState<PrintMode>(
    "document-with-annotations"
  );
  const [pageRange, setPageRange] = useState<PageRange>("all");
  const [customRange, setCustomRange] = useState("");
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [annotationSummary, setAnnotationSummary] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  const getPagesToPrint = (): number[] => {
    switch (pageRange) {
      case "all":
        return Array.from({ length: numPages }, (_, i) => i + 1);
      case "current":
        return [1]; // Would need current page from parent
      case "annotated":
        return [...new Set(annotations.map((ann) => ann.pageNumber))].sort(
          (a, b) => a - b
        );
      case "range":
        if (!customRange) return [];
        try {
          const ranges = customRange.split(",").map((range) => range.trim());
          const pages: number[] = [];

          for (const range of ranges) {
            if (range.includes("-")) {
              const [start, end] = range
                .split("-")
                .map((n) => Number.parseInt(n.trim()));
              if (
                start &&
                end &&
                start <= end &&
                start >= 1 &&
                end <= numPages
              ) {
                for (let i = start; i <= end; i++) {
                  pages.push(i);
                }
              }
            } else {
              const page = Number.parseInt(range);
              if (page >= 1 && page <= numPages) {
                pages.push(page);
              }
            }
          }

          return [...new Set(pages)].sort((a, b) => a - b);
        } catch {
          return [];
        }
      default:
        return [];
    }
  };

  const generatePrintContent = async () => {
    setIsGenerating(true);

    try {
      const pagesToPrint = getPagesToPrint();
      if (pagesToPrint.length === 0) {
        toast("No pages to print", {
          description: "Please select valid pages to print",
        });
        return;
      }

      let printContent = `
        <html>
          <head>
            <title>PDF Print - ${pagesToPrint.length} pages</title>
            <style>
              @media print {
                body { margin: 0; padding: 0; }
                .page-break { page-break-after: always; }
                .no-print { display: none; }
              }
              body { font-family: Arial, sans-serif; }
              .page { margin-bottom: 20px; padding: 20px; border: 1px solid #ddd; }
              .page-header { border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 15px; }
              .annotation { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; background: #f9f9f9; }
              .annotation-header { font-weight: bold; margin-bottom: 5px; }
              .metadata { font-size: 12px; color: #666; margin-top: 20px; }
            </style>
          </head>
          <body>
      `;

      if (includeMetadata) {
        printContent += `
          <div class="metadata">
            <h1>PDF Print Summary</h1>
            <p><strong>Print Date:</strong> ${new Date().toLocaleString()}</p>
            <p><strong>Total Pages:</strong> ${numPages}</p>
            <p><strong>Pages to Print:</strong> ${pagesToPrint.join(", ")}</p>
            <p><strong>Total Annotations:</strong> ${annotations.length}</p>
            <p><strong>Print Mode:</strong> ${printMode
              .replace(/-/g, " ")
              .toUpperCase()}</p>
          </div>
          <div class="page-break"></div>
        `;
      }

      for (const pageNum of pagesToPrint) {
        const pageAnnotations = annotations.filter(
          (ann) => ann.pageNumber === pageNum
        );

        printContent += `<div class="page">`;
        printContent += `<div class="page-header"><h2>Page ${pageNum}</h2></div>`;

        if (printMode === "document-only") {
          printContent += `<p><em>Document content would be rendered here</em></p>`;
        } else if (printMode === "annotations-only") {
          if (pageAnnotations.length > 0) {
            pageAnnotations.forEach((ann) => {
              printContent += `
                <div class="annotation" style="border-left-color: ${
                  ann.color
                };">
                  <div class="annotation-header">
                    ${ann.type.toUpperCase()} - ${ann.content || "No content"}
                  </div>
                  <div style="font-size: 12px; color: #666;">
                    Author: ${ann.author} | Created: ${formatAnnotationDate(ann)}
                  </div>
                </div>
              `;
            });
          } else {
            printContent += `<p><em>No annotations on this page</em></p>`;
          }
        } else {
          // document-with-annotations
          printContent += `<p><em>Document content would be rendered here</em></p>`;

          if (pageAnnotations.length > 0) {
            printContent += `<h3>Annotations:</h3>`;
            pageAnnotations.forEach((ann) => {
              printContent += `
                <div class="annotation" style="border-left-color: ${
                  ann.color
                };">
                  <div class="annotation-header">
                    ${ann.type.toUpperCase()} - ${ann.content || "No content"}
                  </div>
                  <div style="font-size: 12px; color: #666;">
                    Author: ${ann.author} | Created: ${formatAnnotationDate(ann)}
                  </div>
                </div>
              `;
            });
          }
        }

        printContent += `</div>`;
        if (pageNum !== pagesToPrint[pagesToPrint.length - 1]) {
          printContent += `<div class="page-break"></div>`;
        }
      }

      if (annotationSummary && annotations.length > 0) {
        printContent += `
          <div class="page-break"></div>
          <div class="page">
            <h2>Annotation Summary</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #f0f0f0;">
                  <th style="border: 1px solid #ddd; padding: 8px;">Page</th>
                  <th style="border: 1px solid #ddd; padding: 8px;">Type</th>
                  <th style="border: 1px solid #ddd; padding: 8px;">Content</th>
                  <th style="border: 1px solid #ddd; padding: 8px;">Author</th>
                  <th style="border: 1px solid #ddd; padding: 8px;">Date</th>
                </tr>
              </thead>
              <tbody>
        `;

        annotations
          .filter((ann) => pagesToPrint.includes(ann.pageNumber))
          .forEach((ann) => {
            printContent += `
              <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">${
                  ann.pageNumber
                }</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${
                  ann.type
                }</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${
                  ann.content || "No content"
                }</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${
                  ann.author
                }</td>
                <td style="border: 1px solid #ddd; padding: 8px;">${formatAnnotationDateShort(ann)}</td>
              </tr>
            `;
          });

        printContent += `
              </tbody>
            </table>
          </div>
        `;
      }

      printContent += `</body></html>`;

      // Open print window
      const printWindow = window.open("", "_blank");
      if (printWindow) {
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();

        // Auto-print after content loads
        setTimeout(() => {
          printWindow.print();
        }, 500);

        toast("Print dialog opened", {
          description: `Prepared ${pagesToPrint.length} pages for printing`,
        });
      } else {
        toast("Print failed", {
          description: "Unable to open print window. Please check popup blockers.",
        });
      }
    } catch (error) {
      toast("Print generation failed", {
        description: "An error occurred while preparing the print content",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const previewPrint = () => {
    toast("Print preview", {
      description: "Print preview would open in a new window",
    });
  };

  const pagesToPrint = getPagesToPrint();
  const annotatedPages = new Set(annotations.map((ann) => ann.pageNumber));

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Printer className="h-5 w-5" />
          Print Manager
        </CardTitle>
        <CardDescription>
          Print your PDF with or without annotations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Print Mode */}
        <div>
          <label className="text-sm font-medium">Print Mode</label>
          <Select
            value={printMode}
            onValueChange={(value: PrintMode) => setPrintMode(value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="document-only">Document Only</SelectItem>
              <SelectItem value="annotations-only">Annotations Only</SelectItem>
              <SelectItem value="document-with-annotations">
                Document + Annotations
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Page Range */}
        <div>
          <label className="text-sm font-medium">Page Range</label>
          <Select
            value={pageRange}
            onValueChange={(value: PageRange) => setPageRange(value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Pages ({numPages})</SelectItem>
              <SelectItem value="current">Current Page</SelectItem>
              <SelectItem value="annotated">
                Pages with Annotations ({annotatedPages.size})
              </SelectItem>
              <SelectItem value="range">Custom Range</SelectItem>
            </SelectContent>
          </Select>

          {pageRange === "range" && (
            <div className="mt-2">
              <Input
                placeholder="e.g., 1-5, 8, 10-12"
                value={customRange}
                onChange={(e) => setCustomRange(e.target.value)}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Use commas to separate pages/ranges (e.g., 1-5, 8, 10-12)
              </p>
            </div>
          )}
        </div>

        {/* Print Options */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-metadata"
              checked={includeMetadata}
              onCheckedChange={(checked) =>
                setIncludeMetadata(checked as boolean)
              }
            />
            <label htmlFor="include-metadata" className="text-sm">
              Include print metadata and summary
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="annotation-summary"
              checked={annotationSummary}
              onCheckedChange={(checked) =>
                setAnnotationSummary(checked as boolean)
              }
            />
            <label htmlFor="annotation-summary" className="text-sm">
              Include annotation summary table
            </label>
          </div>
        </div>

        {/* Print Summary */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium">Pages to Print</div>
              <div className="text-muted-foreground">
                {pagesToPrint.length > 0
                  ? pagesToPrint.length
                  : "None selected"}
              </div>
            </div>
            <div>
              <div className="font-medium">Annotations</div>
              <div className="text-muted-foreground">
                {
                  annotations.filter((ann) =>
                    pagesToPrint.includes(ann.pageNumber)
                  ).length
                }{" "}
                included
              </div>
            </div>
          </div>

          {pagesToPrint.length > 0 && (
            <div className="mt-3">
              <div className="text-sm font-medium mb-2">Page Numbers:</div>
              <div className="flex flex-wrap gap-1">
                {pagesToPrint.slice(0, 10).map((page) => (
                  <Badge key={page} variant="secondary" className="text-xs">
                    {page}
                  </Badge>
                ))}
                {pagesToPrint.length > 10 && (
                  <Badge variant="outline" className="text-xs">
                    +{pagesToPrint.length - 10} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={generatePrintContent}
            disabled={isGenerating || pagesToPrint.length === 0}
            className="flex-1"
          >
            <Printer className="h-4 w-4 mr-2" />
            {isGenerating ? "Preparing..." : "Print"}
          </Button>

          <Button variant="outline" onClick={previewPrint}>
            <Eye className="h-4 w-4" />
          </Button>
        </div>

        {pagesToPrint.length === 0 && (
          <div className="text-center py-4 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No pages selected for printing</p>
            <p className="text-sm">Please select a valid page range</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
